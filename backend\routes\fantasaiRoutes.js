import express from "express";
import * as dotenv from "dotenv";
import axios from "axios";
dotenv.config();

const router = express.Router();

const handleImageGeneration = async (req, res) => {
  try {
    const {
      prompt,
      width = 1024,
      height = 1024,
      steps = 4,
      seed = 0,
    } = req.body;

    if (!prompt) {
      return res.status(400).json({
        success: false,
        error: "Prompt is required",
      });
    }

    console.log("Generating image with prompt:", prompt);

    const invokeUrl =
      "https://ai.api.nvidia.com/v1/genai/black-forest-labs/flux.1-schnell";

    const headers = {
      Authorization: `Bearer ${process.env.NVDIA_FLUX_API_KEY}`,
      Accept: "application/json",
      "Content-Type": "application/json",
    };

    // Flux model payload format
    const payload = {
      prompt: prompt,
      width: width,
      height: height,
      seed: seed,
      steps: steps,
    };

    console.log("Making request to:", invokeUrl);
    console.log("Payload:", JSON.stringify(payload, null, 2));

    const response = await axios.post(invokeUrl, payload, { headers });

    console.log("Response status:", response.status);
    console.log("Response data:", JSON.stringify(response.data, null, 2));

    // Handle Flux API response format
    if (response.data) {
      // Return the response data to the client
      res.json({
        success: true,
        data: response.data,
      });
    } else {
      throw new Error("No data found in API response");
    }
  } catch (error) {
    console.error("Error:", error.response?.data || error.message);

    // Handle specific HTTP error responses
    if (error.response) {
      const errorMessage =
        error.response.data ||
        `Request failed with status ${error.response.status}`;
      res.status(error.response.status).json({
        success: false,
        error: errorMessage,
      });
    } else {
      res.status(500).json({
        success: false,
        error: error.message || "Failed to generate image",
      });
    }
  }
};

router.route("/").post(handleImageGeneration);

export default router;
