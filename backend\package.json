{"name": "backend", "version": "1.0.0", "description": "", "type": "module", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js"}, "keywords": [], "author": "<PERSON>", "license": "ISC", "dependencies": {"axios": "^1.7.9", "bcryptjs": "^3.0.2", "cloudinary": "^2.5.1", "cookie": "^1.0.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "google-auth-library": "^9.15.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.4", "node-fetch": "^3.3.2"}, "devDependencies": {"nodemon": "^3.1.9"}, "overrides": {"whatwg-url": "14.1.0"}}